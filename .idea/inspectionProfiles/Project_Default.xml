<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="1" class="java.lang.String" itemvalue="requests" />
            <item index="2" class="java.lang.String" itemvalue="pandas" />
            <item index="3" class="java.lang.String" itemvalue="tushare" />
            <item index="4" class="java.lang.String" itemvalue="baostock" />
            <item index="5" class="java.lang.String" itemvalue="tabulate" />
            <item index="6" class="java.lang.String" itemvalue="matplotlib" />
            <item index="7" class="java.lang.String" itemvalue="wxpython" />
            <item index="8" class="java.lang.String" itemvalue="statsmodels" />
            <item index="9" class="java.lang.String" itemvalue="mplfinance" />
            <item index="10" class="java.lang.String" itemvalue="pyecharts" />
            <item index="11" class="java.lang.String" itemvalue="numpy" />
            <item index="12" class="java.lang.String" itemvalue="pywencai" />
            <item index="13" class="java.lang.String" itemvalue="py-mini-racer" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>