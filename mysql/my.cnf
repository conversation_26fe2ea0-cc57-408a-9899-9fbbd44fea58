[mysqld]
# 基本设置
max_connections = 1000
default_authentication_plugin = mysql_native_password
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 缓冲池设置
innodb_buffer_pool_size = 1G
innodb_log_buffer_size = 16M

# 日志设置
slow_query_log = 1
long_query_time = 2
slow_query_log_file = /var/lib/mysql/mysql-slow.log

# 性能优化
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1

[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4 