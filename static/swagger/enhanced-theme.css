/* WeChatPadPro-861 增强版主题 - 动态标签和优化样式 */

/* 标签分组增强样式 */
.swagger-ui .opblock-tag {
  position: relative;
  font-size: 18px;
  font-weight: 600;
  margin: 15px 0;
  padding: 15px 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 标签分组左侧彩色指示条 */
.swagger-ui .opblock-tag::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: linear-gradient(to bottom, #3498db, #9b59b6);
  border-radius: 4px 0 0 4px;
}

/* 标签分组悬停效果 */
.swagger-ui .opblock-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
}

/* 标签名称样式 */
.swagger-ui .opblock-tag span {
  color: #2c3e50;
  font-weight: 600;
  font-size: 18px;
}

/* 标签描述样式 */
.swagger-ui .opblock-tag small {
  color: #7f8c8d;
  font-weight: normal;
  margin-left: 8px;
  font-size: 14px;
}

/* 标签右侧展开/折叠图标 */
.swagger-ui .opblock-tag svg {
  transition: transform 0.3s ease;
}

.swagger-ui .opblock-tag[aria-expanded="true"] svg {
  transform: rotate(180deg);
}

/* 动态背景效果 */
.swagger-ui .opblock-tag::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(120deg, rgba(240, 240, 240, 0) 30%, rgba(240, 240, 240, 0.5) 38%, rgba(240, 240, 240, 0.5) 40%, rgba(240, 240, 240, 0) 48%);
  background-size: 200% 100%;
  background-position: 100% 0;
  z-index: 1;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.swagger-ui .opblock-tag:hover::after {
  opacity: 1;
  animation: shine 1.5s infinite;
}

/* 为不同的标签设置不同的颜色 */
.swagger-ui .opblock-tag[data-tag="admin"] span,
.swagger-ui .opblock-tag[data-tag="管理"] span {
  color: #e74c3c;
}

.swagger-ui .opblock-tag[data-tag="login"] span,
.swagger-ui .opblock-tag[data-tag="登录"] span {
  color: #3498db;
}

.swagger-ui .opblock-tag[data-tag="message"] span,
.swagger-ui .opblock-tag[data-tag="消息"] span {
  color: #2ecc71;
}

.swagger-ui .opblock-tag[data-tag="user"] span,
.swagger-ui .opblock-tag[data-tag="用户"] span {
  color: #9b59b6;
}

.swagger-ui .opblock-tag[data-tag="group"] span,
.swagger-ui .opblock-tag[data-tag="群组"] span {
  color: #f39c12;
}

.swagger-ui .opblock-tag[data-tag="friend"] span,
.swagger-ui .opblock-tag[data-tag="朋友"] span {
  color: #16a085;
}

.swagger-ui .opblock-tag[data-tag="pay"] span,
.swagger-ui .opblock-tag[data-tag="支付"] span {
  color: #d35400;
}

.swagger-ui .opblock-tag[data-tag="favor"] span,
.swagger-ui .opblock-tag[data-tag="收藏"] span {
  color: #8e44ad;
}

.swagger-ui .opblock-tag[data-tag="finder"] span,
.swagger-ui .opblock-tag[data-tag="视频号"] span {
  color: #2980b9;
}

.swagger-ui .opblock-tag[data-tag="friendcircle"] span,
.swagger-ui .opblock-tag[data-tag="朋友圈"] span {
  color: #27ae60;
}

/* 为不同的标签设置不同的左侧指示条颜色 */
.swagger-ui .opblock-tag[data-tag="admin"]::before,
.swagger-ui .opblock-tag[data-tag="管理"]::before {
  background: linear-gradient(to bottom, #e74c3c, #c0392b);
}

.swagger-ui .opblock-tag[data-tag="login"]::before,
.swagger-ui .opblock-tag[data-tag="登录"]::before {
  background: linear-gradient(to bottom, #3498db, #2980b9);
}

.swagger-ui .opblock-tag[data-tag="message"]::before,
.swagger-ui .opblock-tag[data-tag="消息"]::before {
  background: linear-gradient(to bottom, #2ecc71, #27ae60);
}

.swagger-ui .opblock-tag[data-tag="user"]::before,
.swagger-ui .opblock-tag[data-tag="用户"]::before {
  background: linear-gradient(to bottom, #9b59b6, #8e44ad);
}

.swagger-ui .opblock-tag[data-tag="group"]::before,
.swagger-ui .opblock-tag[data-tag="群组"]::before {
  background: linear-gradient(to bottom, #f39c12, #d35400);
}

.swagger-ui .opblock-tag[data-tag="friend"]::before,
.swagger-ui .opblock-tag[data-tag="朋友"]::before {
  background: linear-gradient(to bottom, #16a085, #1abc9c);
}

.swagger-ui .opblock-tag[data-tag="pay"]::before,
.swagger-ui .opblock-tag[data-tag="支付"]::before {
  background: linear-gradient(to bottom, #d35400, #e67e22);
}

.swagger-ui .opblock-tag[data-tag="favor"]::before,
.swagger-ui .opblock-tag[data-tag="收藏"]::before {
  background: linear-gradient(to bottom, #8e44ad, #9b59b6);
}

.swagger-ui .opblock-tag[data-tag="finder"]::before,
.swagger-ui .opblock-tag[data-tag="视频号"]::before {
  background: linear-gradient(to bottom, #2980b9, #3498db);
}

.swagger-ui .opblock-tag[data-tag="friendcircle"]::before,
.swagger-ui .opblock-tag[data-tag="朋友圈"]::before {
  background: linear-gradient(to bottom, #27ae60, #2ecc71);
}

/* 标签右侧中文说明 */
.swagger-ui .opblock-tag .tag-description {
  font-size: 14px;
  color: #7f8c8d;
  margin-left: 10px;
  font-weight: normal;
}

/* 闪光动画效果 */
@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 增强版头部样式 */
.swagger-ui .topbar {
  background: linear-gradient(135deg, #2c3e50, #3498db);
  height: 60px;
  padding: 0;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* 动态背景元素 */
.swagger-ui .topbar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48ZGVmcz48cGF0dGVybiBpZD0icGF0dGVybiIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiPjxwYXRoIGQ9Ik0gMCwyNSBMIDUwLDAgTCAxMDAsMjUgTCA1MCw1MCBaIiBmaWxsPSJub25lIiBzdHJva2U9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz48cGF0aCBkPSJNIDAsNzUgTCA1MCw1MCBMIDEwMCw3NSBMIDU1LDEwMCBaIiBmaWxsPSJub25lIiBzdHJva2U9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
  opacity: 0.3;
  animation: moveBackground 60s linear infinite;
  pointer-events: none;
}

/* 背景动画 */
@keyframes moveBackground {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 信息区域增强 */
.swagger-ui .info {
  margin: 30px 0;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border-top: 4px solid #3498db;
}

.swagger-ui .info .title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

/* 标题下划线效果 */
.swagger-ui .info .title::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #3498db, #9b59b6);
  border-radius: 3px;
}

/* 描述文本增强 */
.swagger-ui .info .description {
  font-size: 16px;
  line-height: 1.6;
  color: #34495e;
}

/* API操作块增强 */
.swagger-ui .opblock {
  margin: 0 0 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;
}

.swagger-ui .opblock:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 操作摘要增强 */
.swagger-ui .opblock .opblock-summary {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.swagger-ui .opblock .opblock-summary-method {
  font-weight: 600;
  border-radius: 4px;
  padding: 6px 12px;
  min-width: 80px;
  text-align: center;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 路径样式增强 */
.swagger-ui .opblock-summary-path {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 15px;
  font-weight: 500;
  color: #2c3e50;
  padding: 0 10px;
  position: relative;
}

/* 路径悬停效果 */
.swagger-ui .opblock-summary-path:hover {
  color: #3498db;
}

/* 描述样式增强 */
.swagger-ui .opblock-summary-description {
  font-size: 14px;
  color: #7f8c8d;
  font-style: italic;
}

/* 自定义表单区域增强 */
#xxcaibi {
  background-color: #ffffff;
  border: none;
  border-radius: 10px;
  padding: 25px;
  margin: 25px auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  width: 90%;
  position: relative;
  overflow: hidden;
}

#xxcaibi::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #3498db, #9b59b6, #2ecc71);
}

/* 表单组样式增强 */
.xxcaibi-group {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

/* 标签样式增强 */
.xxcaibi-label {
  color: #34495e;
  font-size: 15px;
  width: 130px;
  font-weight: 500;
}

/* 输入框样式增强 */
.xxcaibi-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
}

.xxcaibi-input:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  background-color: #ffffff;
}

/* 开关样式增强 */
.xxcaibi-switch {
  width: 50px;
  height: 24px;
  appearance: none;
  background-color: #e0e0e0;
  outline: none;
  border-radius: 24px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.xxcaibi-switch:checked {
  background-color: #3498db;
}

.xxcaibi-switch::before {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.xxcaibi-switch:checked::before {
  transform: translateX(26px);
}

/* 单选按钮组样式增强 */
.radio-group {
  display: flex;
  justify-content: space-between;
  width: 80%;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 5px;
}

.radio-group label {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-group label:hover {
  background-color: #e0e0e0;
}

.radio-group input[type="radio"] {
  margin-right: 10px;
  transform: scale(1.2);
  accent-color: #3498db;
}

.radio-group input[type="radio"]:checked + span {
  color: #3498db;
  font-weight: 500;
}

/* 自定义复制按钮增强 */
button[copy="true"] {
  background: linear-gradient(to right, #3498db, #2980b9) !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 15px !important;
  font-size: 13px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

button[copy="true"]:hover {
  background: linear-gradient(to right, #2980b9, #3498db) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .swagger-ui .opblock-tag {
    padding: 12px 15px;
  }
  
  .swagger-ui .opblock-summary-method {
    min-width: 60px;
    padding: 4px 8px;
  }
  
  .xxcaibi-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .xxcaibi-label {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .radio-group {
    width: 100%;
    flex-direction: column;
  }
  
  .radio-group label {
    margin-bottom: 5px;
  }
} 