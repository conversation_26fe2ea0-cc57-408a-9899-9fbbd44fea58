basePath: ""
consumes:
- application/json
- multipart/form-data
definitions:
  AlisaModel:
    properties:
      Alisa:
        example: ""
        type: string
    title: AlisaModel
    type: object
  AppMessageItem:
    properties:
      ContentType:
        description: ' 2001:(红包消息)'
        format: uint32
        type: integer
      ContentXML:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: AppMessageItem
    type: object
  AppMessageModel:
    properties:
      AppList:
        items:
          $ref: '#/definitions/AppMessageItem'
        type: array
    title: AppMessageModel
    type: object
  AppletModel:
    properties:
      AppId:
        description: ' 应用ID'
        example: ""
        type: string
      Data:
        description: ' 小程序云函数操作的 Data; json字符串, 注意必须是 json 字符串; 传空时默认值为: ''{"with_credentials":true,"from_component":true,"data":{"lang":"zh_CN"},"api_name":"webapi_getuserinfo"}'''
        example: ""
        type: string
      Opt:
        description: ' 小程序云函数操作的 Opt; 默认为1'
        example: "1"
        format: int32
        type: integer
      PackageName:
        example: ""
        type: string
      SdkName:
        example: ""
        type: string
    title: AppletModel
    type: object
  BatchGetContactModel:
    properties:
      RoomWxIDList:
        items:
          type: string
        type: array
      UserNames:
        items:
          type: string
        type: array
    title: BatchGetContactModel
    type: object
  CdnUploadVideoRequest:
    properties:
      AesKey:
        example: ""
        type: string
      CdnThumbAesKey:
        example: ""
        type: string
      CdnThumbImgHeight:
        format: uint32
        type: integer
      CdnThumbImgSize:
        format: uint32
        type: integer
      CdnThumbImgWidth:
        format: uint32
        type: integer
      CdnThumbUrl:
        example: ""
        type: string
      CdnVideoUrl:
        example: ""
        type: string
      Length:
        format: uint32
        type: integer
      Md5:
        example: ""
        type: string
      PlayLength:
        format: uint32
        type: integer
      ThumbData:
        description: ' 普通上传用'
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
      VideoData:
        description: ' 普通上传用'
        items:
          format: int
          type: integer
        type: array
      XmlContent:
        description: ' 直接传xml时用'
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: CdnUploadVideoRequest
    type: object
    x-media-format: video
    x-media-type: true
  ChatRoomWxIdListModel:
    properties:
      ChatRoomWxIdList:
        items:
          type: string
        type: array
    title: ChatRoomWxIdListModel
    type: object
  ChatroomMemberModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      UserList:
        items:
          type: string
        type: array
    title: ChatroomMemberModel
    type: object
  ChatroomNameModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Nickname:
        example: ""
        type: string
    title: ChatroomNameModel
    type: object
  CollectmoneyModel:
    properties:
      InvalidTime:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
      TransFerId:
        example: ""
        type: string
      TransactionId:
        example: ""
        type: string
    title: CollectmoneyModel
    type: object
  ConfirmPreTransfer:
    properties:
      BankSerial:
        description: ' 付款方式 Serial序列号'
        example: ""
        type: string
      BankType:
        description: ' 付款方式 类型'
        example: ""
        type: string
      PayPassword:
        description: ' 支付密码'
        example: ""
        type: string
      ReqKey:
        description: ' 创建转账返回的ReqKey'
        example: ""
        type: string
    title: ConfirmPreTransfer
    type: object
  CreateChatRoomModel:
    properties:
      TopIc:
        example: ""
        type: string
      UserList:
        items:
          type: string
        type: array
    title: CreateChatRoomModel
    type: object
  CreatePreTransfer:
    properties:
      Description:
        description: ' 转账备注'
        example: ""
        type: string
      Fee:
        description: ' 转账金额(单位为分)'
        format: uint32
        type: integer
      ToUserName:
        description: ' 要转账用户的wxid'
        example: ""
        type: string
    title: CreatePreTransfer
    type: object
  DataSection:
    properties:
      DataLen:
        description: ' 数据分包长度(不要超过 65535)'
        example: "61440"
        format: uint32
        type: integer
      StartPos:
        description: ' 数据分包开始位置'
        format: uint32
        type: integer
    title: DataSection
    type: object
  DelContactModel:
    properties:
      DelUserName:
        example: ""
        type: string
    title: DelContactModel
    type: object
  DelSafeDeviceModel:
    properties:
      DeviceUUID:
        example: ""
        type: string
    title: DelSafeDeviceModel
    type: object
  DelayAuthKeyModel:
    properties:
      AuthKey:
        description: ' 要延期的 AuthKey'
        example: ""
        type: string
      Days:
        description: ' AuthKey 的延期天数; Days 小于1默认设置为30'
        example: "30"
        format: int
        type: integer
      ExpiryDate:
        description: ' AuthKey 的到期日期(例如: 2024-01-01); 与 Days 参数只能选其一(优先使用 ExpiryDate
          参数)'
        example: ""
        type: string
      Key:
        description: ' 要延期的 AuthKey'
        example: ""
        type: string
    title: DelayAuthKeyModel
    type: object
  DeleteAuthKeyModel:
    properties:
      AuthKey:
        description: ' 要删除的 AuthKey'
        example: ""
        type: string
      Key:
        description: ' 要删除的 AuthKey'
        example: ""
        type: string
      Opt:
        description: ' 删除操作 0:仅删除授权码 1:删除授权码相关的所有数据'
        format: int
        type: integer
      unknown:
        example: ""
        type: string
    title: DeleteAuthKeyModel
    type: object
  DeviceIdLoginModel:
    properties:
      DeviceInfo:
        $ref: '#/definitions/DeviceInfo'
        description: ' 设备信息'
      LoginData:
        description: ' 62 数据/A16 数据'
        example: ""
        type: string
      Password:
        description: ' 微信密码'
        example: ""
        type: string
      Proxy:
        description: ' socks代理，例如：socks5://username:password@ipv4:port'
        example: ""
        type: string
      Ticket:
        description: ' SMS短信验证码'
        example: ""
        type: string
      Type:
        format: int
        type: integer
      UserName:
        description: ' 手机号'
        example: ""
        type: string
    title: DeviceIdLoginModel
    type: object
  DeviceInfo:
    properties:
      AndroidId:
        example: ""
        type: string
      ImeI:
        example: ""
        type: string
      Manufacturer:
        example: ""
        type: string
      Model:
        example: ""
        type: string
    title: DeviceInfo
    type: object
  DownMediaModel:
    properties:
      AesKey:
        example: ""
        type: string
      FileType:
        format: uint32
        type: integer
      FileURL:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: DownMediaModel
    type: object
    x-media-format: file
    x-media-type: true
  DownloadMediaModel:
    properties:
      Key:
        example: ""
        type: string
      URL:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: DownloadMediaModel
    type: object
    x-media-format: file
    x-media-type: true
  DownloadParam:
    properties:
      CompressType:
        description: ' 下载图片时，数据压缩类型(默认为0即可)'
        format: int
        type: integer
      FromUserName:
        description: ' 下载图片时，图片消息的发送者'
        example: ""
        type: string
      MsgId:
        description: ' 消息ID(注意是msg_id 不是new_msg_id)'
        format: uint32
        type: integer
      Section:
        $ref: '#/definitions/DataSection'
        description: ' 当前要获取的数据分包'
      ToUserName:
        description: ' 下载图片时，图片消息的接收者'
        example: ""
        type: string
      TotalLen:
        description: ' 下载数据的总长度'
        format: int
        type: integer
    title: DownloadParam
    type: object
  DownloadVoiceModel:
    properties:
      Bufid:
        example: ""
        type: string
      Length:
        format: int
        type: integer
      NewMsgId:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: DownloadVoiceModel
    type: object
  ExtDeviceLoginConfirmModel:
    properties:
      Url:
        $ref: '#/definitions/binding:"required"`'
        description: ' 登录URL'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 登录URL'
    title: ExtDeviceLoginConfirmModel
    type: object
  ExtDeviceLoginConfirmParam:
    properties:
      Url:
        description: ' 登录URL'
        example: ""
        type: string
      Wxid:
        description: ' 微信ID'
        example: ""
        type: string
    title: ExtDeviceLoginConfirmParam
    type: object
  ExtDeviceLoginModel:
    properties:
      QrConnect:
        example: ""
        type: string
    title: ExtDeviceLoginModel
    type: object
  FavInfoModel:
    properties:
      FavId:
        format: uint32
        type: integer
      KeyBuf:
        example: ""
        type: string
    title: FavInfoModel
    type: object
  FinderFollowModel:
    properties:
      Cook:
        example: ""
        type: string
      FinderUserName:
        example: ""
        type: string
      OpType:
        format: int32
        type: integer
      PosterUsername:
        example: ""
        type: string
      RefObjectId:
        example: ""
        type: string
      Userver:
        format: int32
        type: integer
    title: FinderFollowModel
    type: object
  FinderSearchModel:
    properties:
      Index:
        format: uint32
        type: integer
      UserKey:
        example: ""
        type: string
      Userver:
        format: int32
        type: integer
      Uuid:
        example: ""
        type: string
    title: FinderSearchModel
    type: object
  FinderUserPrepareModel:
    properties:
      Userver:
        format: int32
        type: integer
    title: FinderUserPrepareModel
    type: object
  FollowGHModel:
    properties:
      GHList:
        items:
          $ref: '#/definitions/VerifyUserItem'
        type: array
    title: FollowGHModel
    type: object
  ForwardImageItem:
    properties:
      AesKey:
        example: ""
        type: string
      CdnMidImgSize:
        format: int32
        type: integer
      CdnMidImgUrl:
        example: ""
        type: string
      CdnThumbImgSize:
        format: int32
        type: integer
      ToUserName:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: ForwardImageItem
    type: object
    x-media-format: image
    x-media-type: true
  ForwardMessageModel:
    properties:
      ForwardImageList:
        items:
          $ref: '#/definitions/ForwardImageItem'
        type: array
      ForwardVideoList:
        items:
          $ref: '#/definitions/ForwardVideoItem'
        type: array
    title: ForwardMessageModel
    type: object
  ForwardVideoItem:
    properties:
      AesKey:
        example: ""
        type: string
      CdnThumbLength:
        format: int
        type: integer
      CdnVideoUrl:
        example: ""
        type: string
      Length:
        format: int
        type: integer
      PlayLength:
        format: int
        type: integer
      ToUserName:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: ForwardVideoItem
    type: object
    x-media-format: video
    x-media-type: true
  GeMaPayQCodeParam:
    properties:
      Money:
        $ref: '#/definitions/binding:"required"`'
        description: ' 金额(单位为分)'
      Name:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款备注'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
    title: GeMaPayQCodeParam
    type: object
  GeMaSkdPayQCodeParam:
    properties:
      Money:
        $ref: '#/definitions/binding:"required"`'
        description: ' 金额(单位为分)'
      Name:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款备注'
      Remark:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款说明'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
    title: GeMaSkdPayQCodeParam
    type: object
  GenAuthKeyModel:
    properties:
      Count:
        description: ' 要生成 AuthKey 的个数; Count小于1默认设置为1'
        example: "1"
        format: int
        type: integer
      Days:
        description: ' AuthKey 的过期天数; Days小于1默认设置为30'
        example: "30"
        format: int
        type: integer
      Remark:
        description: ' 设备备注信息'
        example: ""
        type: string
    title: GenAuthKeyModel
    type: object
  GeneratePayQCodeModel:
    properties:
      Money:
        description: ' 金额(单位为分), 999 即为 9.99 元'
        example: ""
        type: string
      Name:
        description: ' 收款备注'
        example: ""
        type: string
    title: GeneratePayQCodeModel
    type: object
  GetA8KeyRequestModel:
    properties:
      OpCode:
        format: uint32
        type: integer
      ReqUrl:
        example: ""
        type: string
      Scene:
        format: uint32
        type: integer
    title: GetA8KeyRequestModel
    type: object
  GetChatroomMemberDetailModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
    title: GetChatroomMemberDetailModel
    type: object
  GetChatroomQrCodeModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
    title: GetChatroomQrCodeModel
    type: object
  GetContactListModel:
    properties:
      CurrentChatRoomContactSeq:
        format: uint32
        type: integer
      CurrentWxcontactSeq:
        format: uint32
        type: integer
    title: GetContactListModel
    type: object
  GetEncryptInfoParam:
    properties:
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
    title: GetEncryptInfoParam
    type: object
  GetFriendRelationModel:
    properties:
      UserName:
        example: ""
        type: string
    title: GetFriendRelationModel
    type: object
  GetIdDetailModel:
    properties:
      BlackList:
        items:
          type: string
        type: array
      Id:
        example: ""
        type: string
      Location:
        $ref: '#/definitions/baseinfo.Location'
      LocationVal:
        format: int64
        type: integer
    title: GetIdDetailModel
    type: object
  GetLoginQrCodeModel:
    properties:
      Check:
        description: ' 修改代理时(SetProxy接口) 是否发送检测代理请求(可能导致请求超时)'
        example: "false"
        type: boolean
      Proxy:
        description: ' socks代理，例如：socks5://username:password@ipv4:port'
        example: ""
        type: string
    title: GetLoginQrCodeModel
    type: object
  GetMpA8KeyModel:
    properties:
      Opcode:
        format: uint32
        type: integer
      Scene:
        format: int64
        type: integer
      Url:
        example: ""
        type: string
    title: GetMpA8KeyModel
    type: object
  GetMpHistoryMsgModel:
    properties:
      Url:
        example: ""
        type: string
    title: GetMpHistoryMsgModel
    type: object
  GetQrCodeModel:
    properties:
      Recover:
        description: ' 保持默认值, 无需修改'
        type: boolean
      Style:
        description: ' 个人二维码样式: 可设置为8, 其余自行探索'
        example: "8"
        format: uint32
        type: integer
    title: GetQrCodeModel
    type: object
  GetSnsInfoModel:
    properties:
      FirstPageMD5:
        example: ""
        type: string
      MaxID:
        format: uint64
        type: integer
      UserName:
        example: ""
        type: string
    title: GetSnsInfoModel
    type: object
  GetSyncMsgModel:
    properties:
      Key:
        example: ""
        type: string
    title: GetSyncMsgModel
    type: object
  GroupListModel:
    properties:
      Key:
        example: ""
        type: string
    title: GroupListModel
    type: object
  GroupMassMsgImageModel:
    properties:
      ImageBase64:
        example: ""
        type: string
      ToUserName:
        items:
          type: string
        type: array
      file:
        description: 上传的文件内容
        type: file
    title: GroupMassMsgImageModel
    type: object
    x-media-format: image
    x-media-type: true
  GroupMassMsgTextModel:
    properties:
      Content:
        example: ""
        type: string
      ToUserName:
        items:
          type: string
        type: array
    title: GroupMassMsgTextModel
    type: object
  HongBaoDetail:
    properties:
      Offset:
        $ref: '#/definitions/binding:"required"`'
        description: ' 偏移量'
      Size:
        $ref: '#/definitions/binding:"required"`'
        description: ' 大小'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      Xml:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包XML数据'
      int64:
        $ref: '#/definitions/binding:"required"`'
        description: ' 大小'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包XML数据'
    title: HongBaoDetail
    type: object
  HongBaoParam:
    properties:
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      Xml:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包XML数据'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
    title: HongBaoParam
    type: object
  InviteChatroomMembersModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      UserList:
        items:
          type: string
        type: array
    title: InviteChatroomMembersModel
    type: object
  LabelModel:
    properties:
      LabelId:
        example: ""
        type: string
      LabelNameList:
        items:
          type: string
        type: array
      UserLabelList:
        items:
          $ref: '#/definitions/baseinfo.UserLabelInfoItem'
        type: array
    title: LabelModel
    type: object
  MessageItem:
    properties:
      AtWxIDList:
        description: ' 发送艾特消息时的 wxid 列表'
        items:
          type: string
        type: array
      ImageContent:
        description: ' 图片类型消息时图片的 base64 编码'
        example: ""
        type: string
      MsgType:
        description: 1 Text 2 Image
        format: int
        type: integer
      TextContent:
        description: ' 文本类型消息时内容'
        example: ""
        type: string
      ToUserName:
        description: ' 接收者 wxid'
        example: ""
        type: string
    title: MessageItem
    type: object
  ModifyUserInfo:
    properties:
      City:
        example: ""
        type: string
      Country:
        example: ""
        type: string
      InitFlag:
        format: uint32
        type: integer
      NickName:
        example: ""
        type: string
      Province:
        example: ""
        type: string
      Sex:
        format: uint32
        type: integer
      Signature:
        example: ""
        type: string
    title: ModifyUserInfo
    type: object
  MoveContractModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Val:
        format: uint32
        type: integer
    title: MoveContractModel
    type: object
  OpenRedEnvelopesModel:
    properties:
      NativeUrl:
        example: ""
        type: string
    title: OpenRedEnvelopesModel
    type: object
  OpenwxhbParam:
    properties:
      Encrypt_key:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密密钥'
      Encrypt_userinfo:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密用户信息'
      Xml:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包XML数据'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密用户信息'
    title: OpenwxhbParam
    type: object
  PeopleNearbyModel:
    properties:
      Latitude:
        format: double
        type: number
      Longitude:
        format: double
        type: number
    title: PeopleNearbyModel
    type: object
  PhoneLoginModel:
    properties:
      Url:
        example: ""
        type: string
    title: PhoneLoginModel
    type: object
  PrecheckRequest:
    properties:
      '}':
        example: ""
        type: string
      Precheck:
        $ref: '#/definitions/{'
      Ticket:
        example: ""
        type: string
      UUID:
        example: ""
        type: string
      struct:
        $ref: '#/definitions/{'
    title: PrecheckRequest
    type: object
  QRConnectAuthorizeModel:
    properties:
      QrUrl:
        example: ""
        type: string
    title: QRConnectAuthorizeModel
    type: object
  QWAcceptChatRoomModel:
    properties:
      Link:
        example: ""
        type: string
      Opcode:
        format: uint32
        type: integer
    title: QWAcceptChatRoomModel
    type: object
  QWAddChatRoomMemberModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      ToUserName:
        items:
          type: string
        type: array
    title: QWAddChatRoomMemberModel
    type: object
  QWAdminAcceptJoinChatRoomSetModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      P:
        format: int64
        type: integer
    title: QWAdminAcceptJoinChatRoomSetModel
    type: object
  QWApplyAddContactModel:
    properties:
      Content:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
      V1:
        example: ""
        type: string
    title: QWApplyAddContactModel
    type: object
  QWChatRoomTransferOwnerModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: QWChatRoomTransferOwnerModel
    type: object
  QWContactModel:
    properties:
      ChatRoom:
        example: ""
        type: string
      T:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: QWContactModel
    type: object
  QWCreateModel:
    properties:
      ToUserName:
        items:
          type: string
        type: array
    title: QWCreateModel
    type: object
  QWModChatRoomNameModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Name:
        example: ""
        type: string
    title: QWModChatRoomNameModel
    type: object
  QWRemarkModel:
    properties:
      Name:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: QWRemarkModel
    type: object
  QWSyncChatRoomModel:
    properties:
      Key:
        example: ""
        type: string
    title: QWSyncChatRoomModel
    type: object
  QrydetailwxhbParam:
    properties:
      Encrypt_key:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密密钥'
      Encrypt_userinfo:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密用户信息'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      Xml:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包XML数据'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
    title: QrydetailwxhbParam
    type: object
  ReceivewxhbParam:
    properties:
      Encrypt_key:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密密钥'
      Encrypt_userinfo:
        $ref: '#/definitions/binding:"required"`'
        description: ' 加密用户信息'
      InWay:
        $ref: '#/definitions/binding:"required"`'
        description: ' 进入方式'
      Xml:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包XML数据'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 进入方式'
    title: ReceivewxhbParam
    type: object
  RedPacket:
    properties:
      Amount:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包金额'
      Content:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包祝福语'
      Count:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包个数'
      From:
        $ref: '#/definitions/binding:"required"`'
        description: ' 来源'
      RedType:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包类型'
      Username:
        $ref: '#/definitions/binding:"required"`'
        description: ' 接收用户名'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      uint32:
        $ref: '#/definitions/binding:"required"`'
        description: ' 红包金额'
    title: RedPacket
    type: object
  ReplyCommentItem:
    properties:
      NickName:
        description: ' 发表评论的昵称'
        example: ""
        type: string
      OpType:
        description: ' 操作类型：评论/点赞'
        format: uint32
        type: integer
      Source:
        description: ' source'
        format: uint32
        type: integer
      UserName:
        description: ' 评论的微信ID'
        example: ""
        type: string
    title: ReplyCommentItem
    type: object
  ResponseData:
    properties:
      UUID:
        example: ""
        type: string
    title: ResponseData
    type: object
  RevokeMsgModel:
    properties:
      ClientMsgId:
        format: uint64
        type: integer
      CreateTime:
        format: uint64
        type: integer
      NewMsgId:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: RevokeMsgModel
    type: object
  ScanIntoUrlGroupModel:
    properties:
      Url:
        example: ""
        type: string
    title: ScanIntoUrlGroupModel
    type: object
  SearchContactModel:
    properties:
      FromScene:
        format: uint64
        type: integer
      Tg:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
    title: SearchContactModel
    type: object
  SearchContactRequestModel:
    properties:
      FromScene:
        format: uint32
        type: integer
      OpCode:
        description: ' 操作类型'
        format: uint32
        type: integer
      SearchScene:
        description: ' 搜索场景'
        format: uint32
        type: integer
      UserName:
        description: ' 要搜索的内容(微信号、手机号、QQ号等)'
        example: ""
        type: string
    title: SearchContactRequestModel
    type: object
  SendChangePwdRequestModel:
    properties:
      NewPass:
        example: ""
        type: string
      OldPass,:
        example: ""
        type: string
      OpCode:
        format: uint32
        type: integer
    title: SendChangePwdRequestModel
    type: object
  SendEmojiItem:
    properties:
      EmojiMd5:
        example: ""
        type: string
      EmojiSize:
        format: int32
        type: integer
      ToUserName:
        example: ""
        type: string
    title: SendEmojiItem
    type: object
  SendEmojiMessageModel:
    properties:
      EmojiList:
        items:
          $ref: '#/definitions/SendEmojiItem'
        type: array
    title: SendEmojiMessageModel
    type: object
  SendFavItemCircle:
    properties:
      BlackList:
        items:
          type: string
        type: array
      FavItemID:
        format: uint32
        type: integer
      Location:
        $ref: '#/definitions/baseinfo.Location'
      LocationVal:
        format: int64
        type: integer
      SourceID:
        example: ""
        type: string
    title: SendFavItemCircle
    type: object
  SendFileModel:
    properties:
      FileName:
        description: ' 文件名称(可选，如果为空则使用URL中的文件名或本地文件名)'
        example: ""
        type: string
      FileUrl:
        description: ' 文件URL或本地文件路径'
        example: ""
        type: string
      ToUserName:
        description: ' 接收者的微信ID'
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: SendFileModel
    type: object
    x-media-format: file
    x-media-type: true
  SendMessageModel:
    properties:
      MsgItem:
        description: ' 消息体数组'
        items:
          $ref: '#/definitions/MessageItem'
        type: array
    title: SendMessageModel
    type: object
  SendModifyRemarkRequestModel:
    properties:
      RemarkName:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
    title: SendModifyRemarkRequestModel
    type: object
  SendPatModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Scene:
        format: int64
        type: integer
      ToUserName:
        example: ""
        type: string
    title: SendPatModel
    type: object
  SendSnsCommentRequestModel:
    properties:
      SnsCommentList:
        items:
          $ref: '#/definitions/SnsCommentItem'
        type: array
      Tx:
        type: boolean
    title: SendSnsCommentRequestModel
    type: object
  SendSnsObjectOpRequestModel:
    properties:
      SnsObjectOpList:
        items:
          $ref: '#/definitions/SnsObjectOpItem'
        type: array
    title: SendSnsObjectOpRequestModel
    type: object
  SendUploadVoiceRequestModel:
    properties:
      ToUserName:
        example: ""
        type: string
      VoiceData:
        example: ""
        type: string
      VoiceFormat:
        format: int32
        type: integer
      VoiceSecond,:
        format: int32
        type: integer
      file:
        description: 上传的文件内容
        type: file
    title: SendUploadVoiceRequestModel
    type: object
    x-media-format: file
    x-media-type: true
  SendVideoMsgModel:
    properties:
      PlayLength:
        $ref: '#/definitions/binding:"required"`'
        description: ' 视频播放时长（秒）'
      ThumbBase64:
        $ref: '#/definitions/binding:"required"`'
        description: ' 视频缩略图的Base64编码'
      ToWxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 接收者的微信ID'
      VideoBase64:
        $ref: '#/definitions/binding:"required"`'
        description: ' 视频数据的Base64编码'
      file:
        description: 上传的文件内容
        type: file
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 视频缩略图的Base64编码'
      uint32:
        $ref: '#/definitions/binding:"required"`'
        description: ' 视频播放时长（秒）'
    title: SendVideoMsgModel
    type: object
    x-media-format: video
    x-media-type: true
  SetBackgroundImageModel:
    properties:
      Url:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: SetBackgroundImageModel
    type: object
    x-media-format: image
    x-media-type: true
  SetChatroomAccessVerifyModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Enable:
        type: boolean
    title: SetChatroomAccessVerifyModel
    type: object
  SetFriendCircleDaysModel:
    properties:
      Function:
        format: uint32
        type: integer
      Value:
        format: uint32
        type: integer
    title: SetFriendCircleDaysModel
    type: object
  SetSendPatModel:
    properties:
      Value:
        example: ""
        type: string
    title: SetSendPatModel
    type: object
  ShareCardParam:
    properties:
      CardAlias:
        description: ' 名片别名(发送公众号名片时留空)'
        example: ""
        type: string
      CardFlag:
        description: ' 名片CertFlag(0:个人名片 24:公众号名片)'
        format: int
        type: integer
      CardNickName:
        description: ' 名片昵称'
        example: ""
        type: string
      CardWxId:
        description: ' 名片wxid'
        example: ""
        type: string
      ToUserName:
        description: ' 消息接收者'
        example: ""
        type: string
    title: ShareCardParam
    type: object
  SjSkdPayQCodeParam:
    properties:
      Money:
        $ref: '#/definitions/binding:"required"`'
        description: ' 金额(单位为分)'
      Name:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款备注'
      Remark:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款说明'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
    title: SjSkdPayQCodeParam
    type: object
  SnsCommentItem:
    properties:
      Content:
        description: ' 评论内容'
        example: ""
        type: string
      CreateTime:
        description: ' 创建时间'
        format: uint32
        type: integer
      ItemID:
        description: ' 朋友圈项ID'
        example: ""
        type: string
      OpType:
        description: ' 操作类型：评论/点赞'
        format: uint32
        type: integer
      ReplyCommentID:
        description: ' 回复的评论ID'
        format: uint32
        type: integer
      ReplyItem:
        $ref: '#/definitions/ReplyCommentItem'
        description: ' 回覆的评论项'
      ToUserName:
        description: ' 好友微信ID'
        example: ""
        type: string
    title: SnsCommentItem
    type: object
  SnsLocationInfoModel:
    properties:
      City:
        example: ""
        type: string
      Latitude:
        example: ""
        type: string
      Longitude:
        example: ""
        type: string
      PoiAddress:
        example: ""
        type: string
      PoiClassifyID:
        example: ""
        type: string
      PoiClassifyType:
        format: uint32
        type: integer
      PoiClickableStatus:
        format: uint32
        type: integer
      PoiInfoURL:
        example: ""
        type: string
      PoiName:
        example: ""
        type: string
      PoiScale:
        format: int32
        type: integer
    title: SnsLocationInfoModel
    type: object
  SnsMediaItemModel:
    properties:
      Description:
        example: ""
        type: string
      ID:
        format: uint32
        type: integer
      MD5:
        example: ""
        type: string
      Private:
        format: uint32
        type: integer
      SizeHeight:
        example: ""
        type: string
      SizeWidth:
        example: ""
        type: string
      SubType:
        format: uint32
        type: integer
      ThumType:
        example: ""
        type: string
      Thumb:
        example: ""
        type: string
      Title:
        example: ""
        type: string
      TotalSize:
        example: ""
        type: string
      Type:
        format: uint32
        type: integer
      URL:
        example: ""
        type: string
      URLType:
        example: ""
        type: string
      UserData:
        example: ""
        type: string
      VideoDuration:
        format: double
        type: number
      VideoHeight:
        example: ""
        type: string
      VideoMD5:
        example: ""
        type: string
      VideoWidth:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: SnsMediaItemModel
    type: object
    x-media-format: file
    x-media-type: true
  SnsObjectOpItem:
    properties:
      Data:
        description: ' 其它数据'
        items:
          format: int
          type: integer
        type: array
      DataLen:
        description: ' 其它数据长度'
        format: uint32
        type: integer
      Ext:
        format: uint32
        type: integer
      OpType:
        description: ' 操作码'
        format: uint32
        type: integer
      SnsObjID:
        description: ' 朋友圈ID'
        example: ""
        type: string
    title: SnsObjectOpItem
    type: object
  SnsObjectOpRequestModel:
    properties:
      SnsObjectOpList:
        items:
          $ref: '#/definitions/SnsObjectOpItem'
        type: array
    title: SnsObjectOpRequestModel
    type: object
  SnsPostItemModel:
    properties:
      BlackList:
        description: ' 不可见好友列表'
        items:
          type: string
        type: array
      Content:
        description: ' 文本内容'
        example: ""
        type: string
      ContentStyle:
        description: ' 纯文字/图文/引用/视频'
        format: uint32
        type: integer
      ContentUrl:
        example: ""
        type: string
      Description:
        example: ""
        type: string
      GroupUserList:
        description: ' 可见好友列表'
        items:
          type: string
        type: array
      LocationInfo:
        $ref: '#/definitions/SnsLocationInfoModel'
        description: ' 发送朋友圈的位置信息'
      MediaList:
        description: ' 图片/视频列表'
        items:
          $ref: '#/definitions/SnsMediaItemModel'
        type: array
      Privacy:
        description: ' 是否仅自己可见'
        format: uint32
        type: integer
      WithUserList:
        description: ' 提醒好友看列表'
        items:
          type: string
        type: array
    title: SnsPostItemModel
    type: object
  SnsVideoItemModel:
    properties:
      ThumbData:
        items:
          format: int
          type: integer
        type: array
      VideoData:
        items:
          format: int
          type: integer
        type: array
      file:
        description: 上传的文件内容
        type: file
    title: SnsVideoItemModel
    type: object
    x-media-format: video
    x-media-type: true
  SubmitPinRequest:
    properties:
      '}':
        example: ""
        type: string
      Pin:
        example: ""
        type: string
      SubmitPin:
        $ref: '#/definitions/{'
      Ticket:
        example: ""
        type: string
      UUID:
        example: ""
        type: string
      struct:
        $ref: '#/definitions/{'
    title: SubmitPinRequest
    type: object
  SyncMessageModel:
    properties:
      Count:
        description: ' 同步几条消息; 接收空请求体, 默认为0, 同步所有消息'
        format: int
        type: integer
    title: SyncMessageModel
    type: object
  TenPayCollectmoneyModel:
    properties:
      InvalidTime:
        $ref: '#/definitions/binding:"required"`'
        description: ' 失效时间'
      ToUserName:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款用户名'
      TransFerId:
        $ref: '#/definitions/binding:"required"`'
        description: ' 转账ID'
      TransactionId:
        $ref: '#/definitions/binding:"required"`'
        description: ' 交易ID'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 收款用户名'
    title: TenPayCollectmoneyModel
    type: object
  TenPayConfirmPreTransfer:
    properties:
      BankSerial:
        $ref: '#/definitions/binding:"required"`'
        description: ' 付款方式序列号'
      BankType:
        $ref: '#/definitions/binding:"required"`'
        description: ' 付款方式类型'
      PayPasswd:
        $ref: '#/definitions/binding:"required"`'
        description: ' 支付密码'
      ReqKey:
        $ref: '#/definitions/binding:"required"`'
        description: ' 创建转账返回的ReqKey'
      Wxid:
        $ref: '#/definitions/binding:"required"`'
        description: ' 微信ID'
      string:
        $ref: '#/definitions/binding:"required"`'
        description: ' 支付密码'
    title: TenPayConfirmPreTransfer
    type: object
  TransferGroupOwnerModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      NewOwnerUserName:
        example: ""
        type: string
    title: TransferGroupOwnerModel
    type: object
  TransmitFriendCircleModel:
    properties:
      SourceID:
        example: ""
        type: string
    title: TransmitFriendCircleModel
    type: object
  UpdateAutopassModel:
    properties:
      SwitchType:
        format: uint32
        type: integer
    title: UpdateAutopassModel
    type: object
  UpdateChatroomAnnouncementModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Content:
        example: ""
        type: string
    title: UpdateChatroomAnnouncementModel
    type: object
  UpdateNickNameModel:
    properties:
      Scene:
        format: uint32
        type: integer
      Val:
        example: ""
        type: string
    title: UpdateNickNameModel
    type: object
  UpdateSexModel:
    properties:
      City:
        example: ""
        type: string
      Country:
        example: ""
        type: string
      Province:
        example: ""
        type: string
      Sex:
        format: uint32
        type: integer
    title: UpdateSexModel
    type: object
  UpdateStepNumberModel:
    properties:
      Number:
        format: uint64
        type: integer
    title: UpdateStepNumberModel
    type: object
  UploadFileToCDNModel:
    properties:
      FileName:
        description: ' 文件名称(可选，如果为空则使用URL中的文件名或本地文件名)'
        example: ""
        type: string
      FileUrl:
        description: ' 文件URL或本地文件路径'
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: UploadFileToCDNModel
    type: object
    x-media-format: file
    x-media-type: true
  UploadFriendCircleModel:
    properties:
      ImageDataList:
        items:
          type: string
        type: array
      VideoDataList:
        items:
          type: string
        type: array
      file:
        description: 上传的文件内容
        type: file
    title: UploadFriendCircleModel
    type: object
    x-media-format: file
    x-media-type: true
  UploadHeadImageModel:
    properties:
      Base64:
        example: ""
        type: string
      file:
        description: 上传的文件内容
        type: file
    title: UploadHeadImageModel
    type: object
    x-media-format: image
    x-media-type: true
  UploadMContactModel:
    properties:
      Mobile:
        example: ""
        type: string
      MobileList:
        items:
          type: string
        type: array
      file:
        description: 上传的文件内容
        type: file
    title: UploadMContactModel
    type: object
    x-media-format: file
    x-media-type: true
  UserRankLikeModel:
    properties:
      RankId:
        example: ""
        type: string
    title: UserRankLikeModel
    type: object
  VerificationcodeParam:
    properties:
      Code:
        description: ' 必须，用户输入的验证码'
        example: ""
        type: string
      Data62:
        description: ' 可选，后端自动获取，前端无需传递'
        example: ""
        type: string
      Ticket:
        description: ' 可选，后端自动补全，前端无需传递'
        example: ""
        type: string
      Uuid:
        description: ' 必须，二维码接口返回的唯一标识'
        example: ""
        type: string
    title: VerificationcodeParam
    type: object
  VerificationcodeSimpleParam:
    properties:
      Code:
        $ref: '#/definitions/binding:"required,omitempty"`'
        description: ' 用户输入的验证码，通常是6位数字'
      Data62:
        $ref: '#/definitions/binding:"omitempty"`'
        description: ' 设备数据，可选，如果不提供将自动生成'
      Ticket:
        $ref: '#/definitions/binding:"omitempty"`'
        description: ' 验证票据，可选，如果不提供将尝试从登录状态中获取'
      Uuid:
        $ref: '#/definitions/binding:"omitempty"`'
        description: ' 用户标识，可选，如果不提供将使用key参数'
      string:
        $ref: '#/definitions/binding:"omitempty"`'
        description: ' 用户标识，可选，如果不提供将使用key参数'
    title: VerificationcodeSimpleParam
    type: object
  VerifyMethodRequest:
    properties:
      '}':
        example: ""
        type: string
      GetVerifyMethod:
        $ref: '#/definitions/{'
      Ticket:
        example: ""
        type: string
      UUID:
        example: ""
        type: string
      struct:
        $ref: '#/definitions/{'
    title: VerifyMethodRequest
    type: object
  VerifyUserItem:
    properties:
      Gh:
        example: ""
        type: string
      Scene:
        format: int
        type: integer
    title: VerifyUserItem
    type: object
  VerifyUserRequestModel:
    properties:
      ChatRoomUserName:
        description: ' 通过群来添加好友 需要设置此值为群id'
        example: ""
        type: string
      OpCode:
        description: ' 操作类型: 1(免验证发送请求) 2(添加好友/发送验证申请) 3(同意好友/通过好友验证) 4(拒绝好友)'
        example: "2"
        format: uint32
        type: integer
      Scene:
        description: ' 添加来源, 同意添加好友时传回调消息xml中的scene值.<br/>添加好友时的枚举值如下: <br/>1(QQ)
          2(邮箱) 3(微信号) 4(QQ好友) 8(来自群聊) 13(通讯录)<br/>14(群聊) 15(手机号) 18(附近的人) 25(漂流瓶)
          29(摇一摇) 30(二维码)'
        example: "3"
        format: int
        type: integer
      V3:
        description: ' V3用户名数据(SearchContact请求返回的UserValue)'
        example: ""
        type: string
      V4:
        description: ' V4校验数据(SearchContact请求返回的AntispamTicket)'
        example: ""
        type: string
      VerifyContent:
        description: ' 添加好友时的(招呼语/验证信息)'
        example: ""
        type: string
    title: VerifyUserRequestModel
    type: object
  WebhookConfig:
    properties:
      Enabled:
        type: boolean
      IncludeSelfMessage:
        type: boolean
      IndependentMode:
        description: ' 独立模式，不依赖WebSocket（推荐，默认true）'
        type: boolean
      MessageTypes:
        items:
          type: string
        type: array
      RetryCount:
        format: int
        type: integer
      Secret:
        example: ""
        type: string
      Timeout:
        format: int
        type: integer
      URL:
        $ref: '#/definitions/binding:"required"`'
      UseDirectStream:
        description: ' 是否使用直接消息流（推荐，默认true）'
        type: boolean
      UseRedisSync:
        description: ' 是否使用Redis同步（可能有延迟，默认false）'
        type: boolean
      string:
        $ref: '#/definitions/binding:"required"`'
      unknown:
        example: ""
        type: string
    title: WebhookConfig
    type: object
  WebhookTestResponse:
    properties:
      Elapsed:
        description: ' 耗时，单位毫秒'
        format: int64
        type: integer
      Message:
        example: ""
        type: string
      Success:
        type: boolean
    title: WebhookTestResponse
    type: object
  WxAppletQRCodeModel:
    properties:
      AppId:
        description: ' 小程序AppID'
        example: ""
        type: string
      NonceStr:
        description: ' 随机字符串'
        example: ""
        type: string
      Package:
        description: ' 订单详情扩展字符串'
        example: ""
        type: string
      PaySign:
        description: ' 签名'
        example: ""
        type: string
      SessionId:
        description: ' 会话ID，通过JSGetRuntimeSession接口获取'
        example: ""
        type: string
      TimeStamp:
        description: ' 时间戳'
        example: ""
        type: string
    title: WxAppletQRCodeModel
    type: object
  WxBindOpMobileForModel:
    properties:
      OpCode:
        format: int64
        type: integer
      PhoneNumber:
        example: ""
        type: string
      Proxy:
        example: ""
        type: string
      Reg:
        format: uint64
        type: integer
      VerifyCode:
        example: ""
        type: string
    title: WxBindOpMobileForModel
    type: object
  WxFunctionSwitchModel:
    properties:
      Function:
        format: uint32
        type: integer
      Value:
        format: uint32
        type: integer
    title: WxFunctionSwitchModel
    type: object
  baseinfo.ActionInfo:
    properties:
      AppMsg:
        $ref: '#/definitions/SnsAppMsg'
    title: baseinfo.ActionInfo
    type: object
  baseinfo.AlgorithmInfo:
    properties:
      BlockSize:
        description: ' 块大小'
        format: uint32
        type: integer
      Certificate:
        description: ' 证书'
        items:
          format: int
          type: integer
        type: array
      DeviceToken:
        $ref: '#/definitions/wechat.TrustResp'
        description: ' 设备令牌'
      EncryptionType:
        description: ' 加密类型'
        example: ""
        type: string
      Hash:
        description: ' 算法哈希'
        example: ""
        type: string
      IV:
        description: ' 算法IV'
        items:
          format: int
          type: integer
        type: array
      Iterations:
        description: ' 迭代次数'
        format: uint32
        type: integer
      Key:
        description: ' 算法密钥'
        items:
          format: int
          type: integer
        type: array
      KeyLength:
        description: ' 密钥长度'
        format: uint32
        type: integer
      Mode:
        description: ' 加密模式'
        example: ""
        type: string
      Padding:
        description: ' 填充方式'
        example: ""
        type: string
      PrivateKey:
        description: ' 私钥'
        items:
          format: int
          type: integer
        type: array
      PublicKey:
        description: ' 公钥'
        items:
          format: int
          type: integer
        type: array
      Salt:
        description: ' 算法盐值'
        items:
          format: int
          type: integer
        type: array
      SignatureType:
        description: ' 签名类型'
        example: ""
        type: string
      TrustChain:
        description: ' 信任链'
        items:
          format: int
          type: integer
        type: array
      Type:
        description: ' 算法类型'
        example: ""
        type: string
      Version:
        description: ' 算法版本'
        example: ""
        type: string
    title: baseinfo.AlgorithmInfo
    type: object
  baseinfo.AppInfo:
    properties:
      AppName:
        example: ""
        type: string
      FromURL:
        example: ""
        type: string
      ID:
        example: ""
        type: string
      InstallURL:
        example: ""
        type: string
      IsForceUpdate:
        format: uint32
        type: integer
      Version:
        example: ""
        type: string
    title: baseinfo.AppInfo
    type: object
  baseinfo.AppMsg:
    properties:
      AppAttach:
        $ref: '#/definitions/AppAttach'
      AppID:
        example: ""
        type: string
      Content:
        example: ""
        type: string
      Des:
        example: ""
        type: string
      MD5:
        example: ""
        type: string
      SdkVersion:
        format: int
        type: integer
      Title:
        example: ""
        type: string
      Type:
        format: uint32
        type: integer
      URL:
        example: ""
        type: string
    title: baseinfo.AppMsg
    type: object
  baseinfo.ContentObject:
    properties:
      ContentStyle:
        format: uint32
        type: integer
      ContentURL:
        example: ""
        type: string
      Description:
        example: ""
        type: string
      MediaList:
        $ref: '#/definitions/MediaList'
      Title:
        example: ""
        type: string
    title: baseinfo.ContentObject
    type: object
  baseinfo.DeviceInfo:
    properties:
      AdSource:
        description: ' 广告来源'
        example: ""
        type: string
      BaseLibVersion:
        description: ' 基础库版本'
        example: ""
        type: string
      BundleID:
        description: ' 包ID'
        example: ""
        type: string
      CarrierName:
        description: ' 运营商名称'
        example: ""
        type: string
      ClientCheckDataXML:
        description: ' 客户端检查数据XML'
        example: ""
        type: string
      ClientVersion:
        description: ' 客户端版本'
        example: ""
        type: string
      CoreCount:
        description: ' CPU核心数'
        format: uint32
        type: integer
      DPI:
        description: ' 屏幕DPI'
        format: uint32
        type: integer
      DeviceBrand:
        description: ' 设备品牌'
        example: ""
        type: string
      DeviceID:
        description: ' 设备ID'
        items:
          format: int
          type: integer
        type: array
      DeviceModel:
        description: ' 设备型号'
        example: ""
        type: string
      DeviceName:
        description: ' 设备名称'
        example: ""
        type: string
      DeviceType:
        description: ' 设备类型'
        example: ""
        type: string
      GUID2:
        description: ' GUID2'
        example: ""
        type: string
      H5Version:
        description: ' H5版本'
        example: ""
        type: string
      Imei:
        description: ' IMEI'
        example: ""
        type: string
      IphoneVer:
        description: ' iOS版本'
        example: ""
        type: string
      Language:
        description: ' 语言'
        example: ""
        type: string
      LiteAppVersion:
        description: ' 轻应用版本'
        example: ""
        type: string
      MacAddress:
        description: ' MAC地址'
        example: ""
        type: string
      NetworkType:
        description: ' 网络类型'
        example: ""
        type: string
      OsType:
        description: ' 系统类型'
        example: ""
        type: string
      OsTypeNumber:
        description: ' 系统版本号'
        example: ""
        type: string
      RealCountry:
        description: ' 国家'
        example: ""
        type: string
      ScreenHeight:
        description: ' 屏幕高度'
        format: uint32
        type: integer
      ScreenWidth:
        description: ' 屏幕宽度'
        format: uint32
        type: integer
      SoftTypeXML:
        description: ' 软件类型XML'
        example: ""
        type: string
      TimeZone:
        description: ' 时区'
        example: ""
        type: string
      UUIDOne:
        description: ' UUID1'
        example: ""
        type: string
      UUIDTwo:
        description: ' UUID2'
        example: ""
        type: string
      WifiBSSID:
        description: ' WiFi BSSID'
        example: ""
        type: string
      WifiSSID:
        description: ' WiFi SSID'
        example: ""
        type: string
    title: baseinfo.DeviceInfo
    type: object
  baseinfo.Enc:
    properties:
      Key:
        example: ""
        type: string
      Value:
        format: uint32
        type: integer
    title: baseinfo.Enc
    type: object
  baseinfo.GetRedPacketList:
    properties:
      HongBaoItem:
        $ref: '#/definitions/HongBaoURLItem'
      Limit:
        format: int64
        type: integer
      NativeURL:
        example: ""
        type: string
      Offset:
        format: int64
        type: integer
    title: baseinfo.GetRedPacketList
    type: object
  baseinfo.HongBaoItem:
    properties:
      Limit:
        format: int64
        type: integer
      NativeURL:
        example: ""
        type: string
      URLItem:
        $ref: '#/definitions/HongBaoURLItem'
    title: baseinfo.HongBaoItem
    type: object
  baseinfo.HongBaoURLItem:
    properties:
      ChannelID:
        example: ""
        type: string
      MsgType:
        example: ""
        type: string
      SendID:
        example: ""
        type: string
      SendUserName:
        example: ""
        type: string
      ShowSourceMac:
        example: ""
        type: string
      ShowWxPayTitle:
        example: ""
        type: string
      Sign:
        example: ""
        type: string
      Ver:
        example: ""
        type: string
    title: baseinfo.HongBaoURLItem
    type: object
  baseinfo.Location:
    properties:
      City:
        example: ""
        type: string
      Latitude:
        example: ""
        type: string
      Longitude:
        example: ""
        type: string
      PoiAddress:
        example: ""
        type: string
      PoiClassifyID:
        example: ""
        type: string
      PoiClassifyType:
        format: uint32
        type: integer
      PoiName:
        example: ""
        type: string
    title: baseinfo.Location
    type: object
  baseinfo.Media:
    properties:
      Description:
        example: ""
        type: string
      Enc:
        $ref: '#/definitions/Enc'
      ID:
        format: uint64
        type: integer
      Private:
        format: uint32
        type: integer
      Size:
        $ref: '#/definitions/Size'
      SubType:
        format: uint32
        type: integer
      Thumb:
        $ref: '#/definitions/Thumb'
      Title:
        example: ""
        type: string
      Type:
        format: uint32
        type: integer
      URL:
        $ref: '#/definitions/URL'
      UserData:
        example: ""
        type: string
      VideoDuration:
        format: double
        type: number
      VideoSize:
        $ref: '#/definitions/VideoSize'
    title: baseinfo.Media
    type: object
  baseinfo.MediaList:
    properties:
      Media:
        items:
          $ref: '#/definitions/Media'
        type: array
    title: baseinfo.MediaList
    type: object
  baseinfo.RedPacket:
    properties:
      Amount:
        description: ' 每个红包的金额(单位为分, 最小为100); 总金额为 Amount*Count'
        format: uint32
        type: integer
      Content:
        description: ' 红包的备注内容(祝福语)'
        example: ""
        type: string
      Count:
        description: ' 红包个数(最少为1)'
        format: uint32
        type: integer
      From:
        description: ' InAway(0:群红包; 1:个人红包)'
        format: uint32
        type: integer
      RedType:
        description: ' 红包类型(0 普通红包; 1 拼手气红包; ? 专属红包)'
        format: uint32
        type: integer
      Username:
        description: ' 红包接收者; wxid 或 群ID'
        example: ""
        type: string
    title: baseinfo.RedPacket
    type: object
  baseinfo.SecurityInfo:
    properties:
      AlgorithmInfo:
        $ref: '#/definitions/AlgorithmInfo'
        description: ' 算法信息'
      DecryptedData:
        description: ' 解密数据'
        items:
          format: int
          type: integer
        type: array
      DeviceInfo:
        $ref: '#/definitions/DeviceInfo'
        description: ' 设备信息'
      EncryptedData:
        description: ' 加密数据'
        items:
          format: int
          type: integer
        type: array
      LastUpdateTime:
        description: ' 最后更新时间'
        format: uint64
        type: integer
      SecurityLevel:
        description: ' 安全等级'
        format: uint32
        type: integer
      SessionExpireTime:
        description: ' 会话过期时间'
        format: uint64
        type: integer
      SessionID:
        description: ' 会话ID'
        example: ""
        type: string
      SessionKey:
        description: ' 会话密钥'
        items:
          format: int
          type: integer
        type: array
      Signature:
        description: ' 签名'
        items:
          format: int
          type: integer
        type: array
      TrustStatus:
        description: ' 信任状态'
        format: uint32
        type: integer
      VerifyData:
        description: ' 验证数据'
        items:
          format: int
          type: integer
        type: array
    title: baseinfo.SecurityInfo
    type: object
  baseinfo.Size:
    properties:
      Height:
        example: ""
        type: string
      TotalSize:
        example: ""
        type: string
      Width:
        example: ""
        type: string
    title: baseinfo.Size
    type: object
  baseinfo.StreamVideo:
    properties:
      StreamVideoThumbURL:
        example: ""
        type: string
      StreamVideoURL:
        example: ""
        type: string
      StreamVideoWebURL:
        example: ""
        type: string
    title: baseinfo.StreamVideo
    type: object
  baseinfo.Thumb:
    properties:
      EncIdx:
        example: ""
        type: string
      Key:
        example: ""
        type: string
      Token:
        example: ""
        type: string
      Type:
        example: ""
        type: string
      Value:
        example: ""
        type: string
    title: baseinfo.Thumb
    type: object
  baseinfo.TimelineObject:
    properties:
      ActionInfo:
        $ref: '#/definitions/ActionInfo'
      AppInfo:
        $ref: '#/definitions/AppInfo'
      ContentDesc:
        example: ""
        type: string
      ContentDescScene:
        format: uint32
        type: integer
      ContentDescShowType:
        format: uint32
        type: integer
      ContentObject:
        $ref: '#/definitions/ContentObject'
      CreateTime:
        format: uint32
        type: integer
      ID:
        format: uint64
        type: integer
      Location:
        $ref: '#/definitions/Location'
      Private:
        format: uint32
        type: integer
      PublicUserName:
        example: ""
        type: string
      ShowFlag:
        format: uint32
        type: integer
      SightFolded:
        format: uint32
        type: integer
      SourceNickName:
        example: ""
        type: string
      SourceUserName:
        example: ""
        type: string
      StatExtStr:
        example: ""
        type: string
      StatisticsData:
        example: ""
        type: string
      StreamVideo:
        $ref: '#/definitions/StreamVideo'
      UserName:
        example: ""
        type: string
    title: baseinfo.TimelineObject
    type: object
  baseinfo.URL:
    properties:
      EncIdx:
        example: ""
        type: string
      Key:
        example: ""
        type: string
      MD5:
        example: ""
        type: string
      Token:
        example: ""
        type: string
      Type:
        example: ""
        type: string
      Value:
        example: ""
        type: string
      VideoMD5:
        example: ""
        type: string
    title: baseinfo.URL
    type: object
  baseinfo.UserLabelInfoItem:
    properties:
      LabelIDList:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
    title: baseinfo.UserLabelInfoItem
    type: object
  baseinfo.VideoSize:
    properties:
      Height:
        example: ""
        type: string
      Width:
        example: ""
        type: string
    title: baseinfo.VideoSize
    type: object
  wechat.TrustResp:
    properties:
      BaseResponse:
        $ref: '#/definitions/BaseResponse'
        description: ' 基础响应'
      TrustResponseData:
        $ref: '#/definitions/TrustResponseData'
        description: ' 信任响应数据'
    title: wechat.TrustResp
    type: object
  wechat.TrustResponseData:
    properties:
      DeviceToken:
        description: ' 设备令牌'
        example: ""
        type: string
      SoftData:
        $ref: '#/definitions/TrustSoftData'
        description: ' 软件数据'
      Timestamp:
        description: ' 时间戳'
        format: uint64
        type: integer
    title: wechat.TrustResponseData
    type: object
  wechat.TrustSoftData:
    properties:
      SoftConfig:
        description: ' 软件配置'
        example: ""
        type: string
      SoftData:
        description: ' 软件数据'
        items:
          format: int
          type: integer
        type: array
    title: wechat.TrustSoftData
    type: object
info:
  contact:
    email: <EMAIL>
    name: KnowHub技术支持
    url: https://bbs.knowhub.cloud/
  description: "\U0001F680 WeChatPadPro-861 iOS18.6.0\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n\U0001F4F1
    MCP服务器版本\n• 完美兼容MCP服务模型上下文协议\n• 支持长链接、自动心跳、二次登录\n• 重启API服务自动登录\n• 开源生态：兼容HuggingFace/OpenAI等主流模型平台\n•
    微信版本：8.0.6 iOS18.6\n\n✨ 本地服务\n• API模式：SSE\n• API访问：http://localhost:\n• WebSocket：ws://localhost:/ws/GetSyncMsg?key=\n\n\U0001F310
    技术社区与论坛\n• 官方论坛：https://bbs.knowhub.cloud/\n  - AI智能体讨论区：探讨智能体如何重塑数字未来\n  - 智能体MCP交流：MCP服务模型和协议讨论\n
    \ - WeChatPadPro交流：使用经验分享与问题解决\n  - 开发者资源：API文档、插件开发、最佳实践\n\n\U0001F4CC 其他链接\n•
    GitHub：https://github.com/WeChatPadPro/WeChatPadPro\n• Telegram：https://t.me/+LK0JuqLxjmk0ZjRh"
  title: WeChatPadPro-861 iOS18.6.0 最新修订mcp服务器版（SSE模式）
paths:
  /admin/DelayAuthKey:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DelayAuthKeyModel'
      responses:
        "200":
          description: 成功响应
      summary: 延期授权码
      tags:
      - 管理
  /admin/DeleteAuthKey:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeleteAuthKeyModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除授权码
      tags:
      - 管理
  /admin/GenAuthKey1:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GenAuthKeyModel'
      responses:
        "200":
          description: 成功响应
      summary: 生成授权码(新设备)
      tags:
      - 管理
  /admin/GetAllDevices:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取所有设备key
      tags:
      - 管理
  /admin/accountMessageInfo:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取指定账号的消息信息
      tags:
      - 管理
  /admin/multiAccountStatus:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取多账号状态和消息推送情况
      tags:
      - 管理
  /applet/AuthMpLogin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetMpA8KeyModel'
      responses:
        "200":
          description: 成功响应
      summary: 授权公众号登录
      tags:
      - 公众号/小程序
  /applet/FollowGH:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FollowGHModel'
      responses:
        "200":
          description: 成功响应
      summary: 关注公众号
      tags:
      - 公众号/小程序
  /applet/GetA8Key:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetA8KeyRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 授权链接
      tags:
      - 公众号/小程序
  /applet/GetMpA8Key:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetMpA8KeyModel'
      responses:
        "200":
          description: 成功响应
      summary: 授权链接
      tags:
      - 公众号/小程序
  /applet/GetMpHistoryMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetMpHistoryMsgModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取公众号历史消息
      tags:
      - 公众号/小程序
  /applet/JSGetRuntimeSession:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取小程序运行时会话
      tags:
      - 公众号/小程序
  /applet/JSGetSessionidQRcode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/WxAppletQRCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取小程序支付二维码
      tags:
      - 公众号/小程序
  /applet/JSOperateWxData:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: 成功响应
      summary: 小程序云函数操作
      tags:
      - 公众号/小程序
  /applet/JsLogin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: 成功响应
      summary: 授权小程序(返回授权后的code)
      tags:
      - 公众号/小程序
  /applet/QRConnectAuthorize:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QRConnectAuthorizeModel'
      responses:
        "200":
          description: 成功响应
      summary: 二维码授权请求
      tags:
      - 公众号/小程序
  /applet/QRConnectAuthorizeConfirm:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QRConnectAuthorizeModel'
      responses:
        "200":
          description: 成功响应
      summary: 二维码授权确认
      tags:
      - 公众号/小程序
  /applet/SdkOauthAuthorize:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: 成功响应
      summary: app 应用授权
      tags:
      - 公众号/小程序
  /equipment/DelSafeDevice:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DelSafeDeviceModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除安全设备
      tags:
      - 设备
  /equipment/GetBoundHardDevice:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取硬件设备情况
      tags:
      - 设备
  /equipment/GetOnlineInfo:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取在线设备信息
      tags:
      - 设备
  /equipment/GetSafetyInfo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取安全设备列表
      tags:
      - 设备
  /favor/BatchDelFavItem:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FavInfoModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除收藏
      tags:
      - 收藏
  /favor/FavSync:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 同步收藏
      tags:
      - 收藏
  /favor/GetFavItemId:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FavInfoModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取收藏详细
      tags:
      - 收藏
  /favor/GetFavList:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FavInfoModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取收藏list
      tags:
      - 收藏
  /finder/FinderFollow:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FinderFollowModel'
      responses:
        "200":
          description: 成功响应
      summary: 关注取消
      tags:
      - 视频号
  /finder/FinderSearch:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FinderSearchModel'
      responses:
        "200":
          description: 成功响应
      summary: 视频号搜索
      tags:
      - 视频号
  /finder/FinderUserPrepare:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FinderUserPrepareModel'
      responses:
        "200":
          description: 成功响应
      summary: 视频号中心
      tags:
      - 视频号
  /friend/AgreeAdd:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/VerifyUserRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 同意好友请求
      tags:
      - 朋友
  /friend/DelContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DelContactModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除好友
      tags:
      - 朋友
  /friend/GetContactDetailsList:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/BatchGetContactModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取联系人详情
      tags:
      - 朋友
  /friend/GetContactList:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetContactListModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取全部联系人
      tags:
      - 朋友
  /friend/GetFriendList:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取好友列表
      tags:
      - 朋友
  /friend/GetFriendRelation:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetFriendRelationModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取好友关系
      tags:
      - 朋友
  /friend/GetGHList:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取关注的公众号列表
      tags:
      - 朋友
  /friend/GetMFriend:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取手机通讯录好友
      tags:
      - 朋友
  /friend/GroupList:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取保存的群聊列表
      tags:
      - 朋友
  /friend/SearchContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SearchContactRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 搜索联系人
      tags:
      - 朋友
  /friend/UploadMContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadMContactModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 上传手机通讯录好友
      tags:
      - 文件操作
      - 朋友
      x-media-endpoint: true
      x-media-type: file
  /friend/VerifyUser:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/VerifyUserRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 验证好友/添加好友
      tags:
      - 朋友
  /group/AddChatRoomMembers:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/InviteChatroomMembersModel'
      responses:
        "200":
          description: 成功响应
      summary: 添加群成员
      tags:
      - 群管理
  /group/AddChatroomAdmin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatroomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 添加群管理员
      tags:
      - 群管理
  /group/CreateChatRoom:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CreateChatRoomModel'
      responses:
        "200":
          description: 成功响应
      summary: 创建群请求
      tags:
      - 群管理
  /group/DelChatroomAdmin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatroomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除群管理员
      tags:
      - 群管理
  /group/GetAllGroupList:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取所有群列表（包括未保存到通讯录的群）
      tags:
      - 群管理
  /group/GetChatRoomInfo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatRoomWxIdListModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取群详情
      tags:
      - 群管理
  /group/GetChatroomMemberDetail:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomMemberDetailModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取群成员详细
      tags:
      - 群管理
  /group/GetChatroomQrCode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomQrCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取群二维码
      tags:
      - 群管理
  /group/GroupList:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取群列表
      tags:
      - 群管理
  /group/InviteChatroomMembers:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/InviteChatroomMembersModel'
      responses:
        "200":
          description: 成功响应
      summary: 邀请群成员
      tags:
      - 群管理
  /group/MoveToContract:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/MoveContractModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取群聊
      tags:
      - 群管理
  /group/QuitChatroom:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomMemberDetailModel'
      responses:
        "200":
          description: 成功响应
      summary: 退出群聊
      tags:
      - 群管理
  /group/ScanIntoUrlGroup:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ScanIntoUrlGroupModel'
      responses:
        "200":
          description: 成功响应
      summary: 扫码入群
      tags:
      - 群管理
  /group/SendDelDelChatRoomMember:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/InviteChatroomMembersModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除群成员
      tags:
      - 群管理
  /group/SendPat:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendPatModel'
      responses:
        "200":
          description: 成功响应
      summary: 群拍一拍功能
      tags:
      - 群管理
  /group/SendTransferGroupOwner:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/TransferGroupOwnerModel'
      responses:
        "200":
          description: 成功响应
      summary: 转让群
      tags:
      - 群管理
  /group/SetChatroomAccessVerify:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetChatroomAccessVerifyModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置群聊邀请开关
      tags:
      - 群管理
  /group/SetChatroomAnnouncement:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateChatroomAnnouncementModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置群公告
      tags:
      - 群管理
  /group/SetChatroomName:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatroomNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置群昵称
      tags:
      - 群管理
  /group/SetGetChatRoomInfoDetail:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomMemberDetailModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取群公告
      tags:
      - 群管理
  /label/AddContactLabel:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: 成功响应
      summary: 添加列表
      tags:
      - 标签
  /label/DelContactLabel:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除标签
      tags:
      - 标签
  /label/GetContactLabelList:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取标签列表
      tags:
      - 标签
  /label/GetWXFriendListByLabel:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取标签下所有好友
      tags:
      - 标签
  /label/ModifyLabel:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改标签
      tags:
      - 标签
  /login/A16Login:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: 成功响应
      summary: A16数据登录
      tags:
      - 登录
  /login/AutoVerificationcode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/VerificationcodeParam'
      responses:
        "200":
          description: 成功响应
      summary: 自动处理验证码提交（自动获取ticket）
      tags:
      - 登录
  /login/CheckCanSetAlias:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 检测微信登录环境
      tags:
      - 登录
  /login/CheckLoginStatus:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 检测扫码状态
      tags:
      - 登录
  /login/DeviceLogin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: 成功响应
      summary: 62账号密码登录
      tags:
      - 登录
  /login/ForceRefreshToken:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /api/login/ForceRefreshToken [post]'
      tags:
      - 登录
  /login/Get62Data:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 提取62数据
      tags:
      - 登录
  /login/GetAutoRefreshTokenStatus:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /api/login/GetAutoRefreshTokenStatus [get]'
      tags:
      - 登录
  /login/GetIWXConnect:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 打印链接数量
      tags:
      - 登录
  /login/GetInItStatus:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 初始化状态
      tags:
      - 登录
  /login/GetLoginQrCodeNew:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: '@Router /api/login/qr/new [post]'
      tags:
      - 登录
  /login/GetLoginQrCodeNewX:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: '@Router /api/login/qr/newx [post]'
      tags:
      - 登录
  /login/GetLoginStatus:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取在线状态
      tags:
      - 登录
  /login/LogOut:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 退出登录
      tags:
      - 登录
  /login/LoginNew:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: 成功响应
      summary: 62LoginNew新疆号登录
      tags:
      - 登录
  /login/PhoneDeviceLogin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/PhoneLoginModel'
      responses:
        "200":
          description: 成功响应
      summary: 辅助新手机登录
      tags:
      - 登录
  /login/ShowQrCode:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: HTML展示登录二维码
      tags:
      - 登录
  /login/SmsLogin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: 成功响应
      summary: 短信登录
      tags:
      - 登录
  /login/WakeUpLogin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: 唤醒登录(只限扫码登录)
      tags:
      - 登录
  /login/WxBindOpMobileForReg:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/WxBindOpMobileForModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取验证码
      tags:
      - 登录
  /message/AddMessageMgr:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      responses:
        "200":
          description: 成功响应
      summary: 添加要发送的文本消息进入管理器
      tags:
      - 消息
  /message/CdnUploadVideo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CdnUploadVideoRequest'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 上传视频
      tags:
      - 视频操作
      - 消息
      x-media-endpoint: true
      x-media-type: video
  /message/DownloadFile:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 下载文件接口
      tags:
      - 文件操作
      - 消息
      x-media-endpoint: true
      x-media-type: file
  /message/ForwardEmoji:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendEmojiMessageModel'
      - description: 要转发的消息ID
        in: query
        name: msgId
        required: true
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 转发表情，包含动图
      tags:
      - 转发操作
      - 消息
      x-forward-operation: true
      x-media-endpoint: true
      x-media-type: file
  /message/ForwardImageMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ForwardMessageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      - description: 要转发的消息ID
        in: query
        name: msgId
        required: true
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 转发图片
      tags:
      - 转发操作
      - 消息
      x-forward-operation: true
      x-media-endpoint: true
      x-media-type: image
  /message/ForwardVideoMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ForwardMessageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      - description: 要转发的消息ID
        in: query
        name: msgId
        required: true
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 转发视频
      tags:
      - 转发操作
      - 消息
      x-forward-operation: true
      x-media-endpoint: true
      x-media-type: video
  /message/GetMsgBigImg:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadParam'
      responses:
        "200":
          description: 成功响应
      summary: 获取图片(高清图片下载)
      tags:
      - 消息
  /message/GetMsgVideo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadParam'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 获取视频(视频数据下载)
      tags:
      - 视频操作
      - 消息
      x-media-endpoint: true
      x-media-type: video
  /message/GetMsgVoice:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadVoiceModel'
      responses:
        "200":
          description: 成功响应
      summary: 下载语音消息
      tags:
      - 消息
  /message/GroupMassMsgImage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GroupMassMsgImageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 群发图片
      tags:
      - 图片操作
      - 消息
      x-media-endpoint: true
      x-media-type: image
  /message/GroupMassMsgText:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GroupMassMsgTextModel'
      responses:
        "200":
          description: 成功响应
      summary: 群发接口
      tags:
      - 消息
  /message/HttpSyncMsg:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SyncMessageModel'
      responses:
        "200":
          description: 成功响应
      summary: 同步消息, HTTP-轮询方式
      tags:
      - 消息
  /message/NewSyncHistoryMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 同步历史消息
      tags:
      - 消息
  /message/RevokeMsg:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RevokeMsgModel'
      responses:
        "200":
          description: 成功响应
      summary: 撤销消息
      tags:
      - 消息
  /message/RevokeMsgNew:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RevokeMsgModel'
      responses:
        "200":
          description: 成功响应
      summary: 撤回消息（New）
      tags:
      - 消息
  /message/SendAppMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppMessageModel'
      responses:
        "200":
          description: 成功响应
      summary: 发送App消息
      tags:
      - 消息
  /message/SendCdnDownload:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownMediaModel'
      responses:
        "200":
          description: 成功响应
      summary: 下载 请求
      tags:
      - 消息
  /message/SendEmojiMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendEmojiMessageModel'
      responses:
        "200":
          description: 成功响应
      summary: 发送表情
      tags:
      - 消息
  /message/SendFileMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendFileModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 发送文件消息
      tags:
      - 文件操作
      - 消息
      x-media-endpoint: true
      x-media-type: file
  /message/SendImageMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 发送图片消息
      tags:
      - 图片操作
      - 消息
      x-media-endpoint: true
      x-media-type: image
  /message/SendImageNewMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 发送图片消息（New）
      tags:
      - 图片操作
      - 消息
      x-media-endpoint: true
      x-media-type: image
  /message/SendTextMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      responses:
        "200":
          description: 成功响应
      summary: 发送文本消息
      tags:
      - 消息
  /message/SendVideoMsg:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendVideoMsgModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 发送视频消息
      tags:
      - 视频操作
      - 消息
      x-media-endpoint: true
      x-media-type: video
  /message/SendVoice:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendUploadVoiceRequestModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 发送语音
      tags:
      - 文件操作
      - 消息
      x-media-endpoint: true
      x-media-type: file
  /message/ShareCardMessage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ShareCardParam'
      responses:
        "200":
          description: 成功响应
      summary: 分享名片消息
      tags:
      - 消息
  /message/UploadFileToCDN:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadFileToCDNModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 上传文件到微信CDN服务器
      tags:
      - 文件操作
      - 消息
      x-media-endpoint: true
      x-media-type: file
  /other/GetPeopleNearby:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/PeopleNearbyModel'
      responses:
        "200":
          description: 成功响应
      summary: 查看附近的人
      tags:
      - 其他
  /other/GetRedisSyncMsg:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetSyncMsgModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取缓存在redis中的消息
      tags:
      - 其他
  /other/GetUserRankLikeCount:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UserRankLikeModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取步数排行数据列表
      tags:
      - 其他
  /other/UpdateStepNumber:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateStepNumberModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改步数
      tags:
      - 其他
  /pay/AutoHongBao:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/HongBaoParam'
      responses:
        "200":
          description: 成功响应
      summary: 抢红包
      tags:
      - 支付
  /pay/Collectmoney:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CollectmoneyModel'
      responses:
        "200":
          description: 成功响应
      summary: 确定收款
      tags:
      - 支付
  /pay/CollectmoneyNew:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/TenPayCollectmoneyModel'
      responses:
        "200":
          description: 成功响应
      summary: 确认收款
      tags:
      - 支付
  /pay/ConfirmPreTransfer:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ConfirmPreTransfer'
      responses:
        "200":
          description: 成功响应
      summary: 确认转账(修复版)
      tags:
      - 支付
  /pay/ConfirmPreTransferNew:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/TenPayConfirmPreTransfer'
      responses:
        "200":
          description: 成功响应
      summary: 确认支付(新版本)
      tags:
      - 支付
  /pay/CreatePreTransfer:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CreatePreTransfer'
      responses:
        "200":
          description: 成功响应
      summary: 创建转账
      tags:
      - 支付
  /pay/GeMaPayQCode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GeMaPayQCodeParam'
      responses:
        "200":
          description: 成功响应
      summary: 自定义个人收款二维码
      tags:
      - 支付
  /pay/GeMaSkdPayQCode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GeMaSkdPayQCodeParam'
      responses:
        "200":
          description: 成功响应
      summary: 自定义经营个人收款单
      tags:
      - 支付
  /pay/GeneratePayQCode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GeneratePayQCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: 生成自定义收款二维码
      tags:
      - 支付
  /pay/GetBandCardList:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 获取银行卡信息
      tags:
      - 支付
  /pay/GetEncryptInfo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetEncryptInfoParam'
      responses:
        "200":
          description: 成功响应
      summary: 获取加密信息
      tags:
      - 支付
  /pay/GetRedEnvelopesDetail:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/HongBaoItem'
      responses:
        "200":
          description: 成功响应
      summary: 查看红包详情
      tags:
      - 支付
  /pay/GetRedPacketList:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetRedPacketList'
      responses:
        "200":
          description: 成功响应
      summary: 查看红包领取列表
      tags:
      - 支付
  /pay/GetRedPacketListApi:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/HongBaoDetail'
      responses:
        "200":
          description: 成功响应
      summary: 查看红包领取列表(新版本)
      tags:
      - 支付
  /pay/OpenRedEnvelopes:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/HongBaoItem'
      responses:
        "200":
          description: 成功响应
      summary: 拆红包
      tags:
      - 支付
  /pay/Openwxhb:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/OpenwxhbParam'
      responses:
        "200":
          description: 成功响应
      summary: 拆开红包
      tags:
      - 支付
  /pay/Qrydetailwxhb:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QrydetailwxhbParam'
      responses:
        "200":
          description: 成功响应
      summary: 查看红包详情
      tags:
      - 支付
  /pay/Receivewxhb:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ReceivewxhbParam'
      responses:
        "200":
          description: 成功响应
      summary: 打开红包
      tags:
      - 支付
  /pay/SjSkdPayQCode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SjSkdPayQCodeParam'
      responses:
        "200":
          description: 成功响应
      summary: 自定义商家收款单
      tags:
      - 支付
  /pay/WXCreateRedPacket:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RedPacket'
      responses:
        "200":
          description: 成功响应
      summary: 创建红包
      tags:
      - 支付
  /pay/WXCreateRedPacketNew:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RedPacket'
      responses:
        "200":
          description: 成功响应
      summary: 创建红包(新版本)
      tags:
      - 支付
  /qy/QWAcceptChatRoom:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAcceptChatRoomModel'
      responses:
        "200":
          description: 成功响应
      summary: 同意进企业群
      tags:
      - 企业微信
  /qy/QWAddChatRoomMember:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 直接拉朋友进企业群
      tags:
      - 企业微信
  /qy/QWAdminAcceptJoinChatRoomSet:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAdminAcceptJoinChatRoomSetModel'
      responses:
        "200":
          description: 成功响应
      summary: 设定企业群管理审核进群
      tags:
      - 企业微信
  /qy/QWApplyAddContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWApplyAddContactModel'
      responses:
        "200":
          description: 成功响应
      summary: 向企业微信打招呼
      tags:
      - 企业微信
  /qy/QWAppointChatRoomAdmin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 增加企业管理员
      tags:
      - 企业微信
  /qy/QWChatRoomAnnounce:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 发布企业群公告
      tags:
      - 企业微信
  /qy/QWChatRoomTransferOwner:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWChatRoomTransferOwnerModel'
      responses:
        "200":
          description: 成功响应
      summary: 转让企业群
      tags:
      - 企业微信
  /qy/QWContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWContactModel'
      responses:
        "200":
          description: 成功响应
      summary: 提取企业 wx 详情
      tags:
      - 企业微信
  /qy/QWCreateChatRoom:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWCreateModel'
      responses:
        "200":
          description: 成功响应
      summary: 创建企业群
      tags:
      - 企业微信
  /qy/QWDelChatRoom:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除企业群
      tags:
      - 企业微信
  /qy/QWDelChatRoomAdmin:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 移除群管理员
      tags:
      - 企业微信
  /qy/QWDelChatRoomMember:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 删除企业群成员
      tags:
      - 企业微信
  /qy/QWGetChatRoomMember:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 提取企业群全部成员
      tags:
      - 企业微信
  /qy/QWGetChatRoomQR:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 提取企业群二维码
      tags:
      - 企业微信
  /qy/QWGetChatroomInfo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 提取企业群名称公告设定等信息
      tags:
      - 企业微信
  /qy/QWInviteChatRoomMember:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: 成功响应
      summary: 发送群邀请链接
      tags:
      - 企业微信
  /qy/QWModChatRoomMemberNick:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改成员在群中呢称
      tags:
      - 企业微信
  /qy/QWModChatRoomName:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改企业群名称
      tags:
      - 企业微信
  /qy/QWRemark:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWRemarkModel'
      responses:
        "200":
          description: 成功响应
      summary: 备注企业 wxid
      tags:
      - 企业微信
  /qy/QWSearchContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SearchContactModel'
      responses:
        "200":
          description: 成功响应
      summary: 搜手机或企业对外名片链接提取验证
      tags:
      - 企业微信
  /qy/QWSyncChatRoom:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWSyncChatRoomModel'
      responses:
        "200":
          description: 成功响应
      summary: 提取全部企业微信群-
      tags:
      - 企业微信
  /qy/QWSyncContact:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 提取全部的企业通讯录
      tags:
      - 企业微信
  /rabbitmq/error:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /rabbitmq/error [post]'
      tags:
      - /rabbitmq
  /rabbitmq/log:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /rabbitmq/log [post]'
      tags:
      - /rabbitmq
  /rabbitmq/publish:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /rabbitmq/publish [post]'
      tags:
      - /rabbitmq
  /rabbitmq/reconnect:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /rabbitmq/reconnect [post]'
      tags:
      - /rabbitmq
  /rabbitmq/status:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /rabbitmq/status [post]'
      tags:
      - /rabbitmq
  /sns/DownloadMedia:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadMediaModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 下载朋友圈视频
      tags:
      - 文件操作
      - 朋友圈
      x-media-endpoint: true
      x-media-type: file
  /sns/GetCollectCircle:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendFavItemCircle'
      responses:
        "200":
          description: 成功响应
      summary: 获取收藏朋友圈详情
      tags:
      - 朋友圈
  /sns/GetSnsSync:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 同步朋友圈
      tags:
      - 朋友圈
  /sns/SendCdnSnsVideoUploadReuqest:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SnsVideoItemModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 上传朋友圈视频
      tags:
      - 视频操作
      - 朋友圈
      x-media-endpoint: true
      x-media-type: video
  /sns/SendFavItemCircle:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendFavItemCircle'
      responses:
        "200":
          description: 成功响应
      summary: 转发收藏朋友圈id
      tags:
      - 朋友圈
  /sns/SendFriendCircle:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SnsPostItemModel'
      responses:
        "200":
          description: 成功响应
      summary: 发送朋友圈
      tags:
      - 朋友圈
  /sns/SendFriendCircleByXMl:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/TimelineObject'
      responses:
        "200":
          description: 成功响应
      summary: 发送朋友圈XML结构
      tags:
      - 朋友圈
  /sns/SendOneIdCircle:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetIdDetailModel'
      responses:
        "200":
          description: 成功响应
      summary: 一键转发朋友圈
      tags:
      - 朋友圈
  /sns/SendSnsComment:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendSnsCommentRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 点赞评论
      tags:
      - 朋友圈
  /sns/SendSnsObjectDetailById:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetIdDetailModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取指定id朋友圈
      tags:
      - 朋友圈
  /sns/SendSnsObjectOp:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendSnsObjectOpRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 朋友圈操作
      tags:
      - 朋友圈
  /sns/SendSnsTimeLine:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetSnsInfoModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取朋友圈主页
      tags:
      - 朋友圈
  /sns/SendSnsUserPage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetSnsInfoModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取指定人朋友圈
      tags:
      - 朋友圈
  /sns/SetBackgroundImage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetBackgroundImageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 设置朋友圈背景图片
      tags:
      - 图片操作
      - 朋友圈
      x-media-endpoint: true
      x-media-type: image
  /sns/SetFriendCircleDays:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetFriendCircleDaysModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置朋友圈可见天数
      tags:
      - 朋友圈
  /sns/UploadFriendCircleImage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadFriendCircleModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 上传图片信息
      tags:
      - 图片操作
      - 朋友圈
      x-media-endpoint: true
      x-media-type: image
  /user/ChangePwd:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendChangePwdRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 更改密码
      tags:
      - 用户
  /user/GetMyQrCode:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetQrCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: 获取我的二维码
      tags:
      - 用户
  /user/GetProfile:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 获取个人资料信息
      tags:
      - 文件操作
      - 用户
      x-media-endpoint: true
      x-media-type: file
  /user/GetProxyInfo:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /user/GetProxyInfo [get]'
      tags:
      - 用户
  /user/GetProxyPoolInfo:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /user/GetProxyPoolInfo [get]'
      tags:
      - 用户
  /user/ModifyRemark:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendModifyRemarkRequestModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改备注
      tags:
      - 用户
  /user/ModifyUserInfo:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ModifyUserInfo'
      responses:
        "200":
          description: 成功响应
      summary: 修改资料
      tags:
      - 用户
  /user/SetFunctionSwitch:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/WxFunctionSwitchModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置添加我的方式
      tags:
      - 用户
  /user/SetNickName:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateNickNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置昵称
      tags:
      - 用户
  /user/SetProxy:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改Socks5代理
      tags:
      - 用户
  /user/SetSendPat:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetSendPatModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置拍一拍名称
      tags:
      - 用户
  /user/SetSexDq:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateSexModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改性别
      tags:
      - 用户
  /user/SetSignature:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateNickNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改签名
      tags:
      - 用户
  /user/SetWechat:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AlisaModel'
      responses:
        "200":
          description: 成功响应
      summary: 设置微信号
      tags:
      - 用户
  /user/UpdateAutoPass:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateAutopassModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改加好友需要验证属性
      tags:
      - 用户
  /user/UpdateNickName:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateNickNameModel'
      responses:
        "200":
          description: 成功响应
      summary: 修改名称
      tags:
      - 用户
  /user/UploadHeadImage:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadHeadImageModel'
      - description: 上传的文件
        in: formData
        name: file
        type: file
      responses:
        "200":
          description: 成功响应
      summary: 上传头像
      tags:
      - 图片操作
      - 用户
      x-media-endpoint: true
      x-media-type: image
  /webhook/connection:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /webhook/monitor/connection [get]'
      tags:
      - Webhook
  /webhook/heartbeat:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /webhook/monitor/heartbeat [get]'
      tags:
      - Webhook
  /webhook/reconnect:
    post:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: '@Router /webhook/monitor/reconnect [post]'
      tags:
      - Webhook
  /ws/GetSyncMsg:
    get:
      consumes:
      - application/json
      - multipart/form-data
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: 成功响应
      summary: 同步消息，ws协议; 下面有【同步消息-HTTP-轮询方式】
      tags:
      - 同步消息
produces:
- application/json
- application/octet-stream
swagger: "2.0"
tags:
- description: /admin
  name: 管理
- description: /login
  name: 登录
- description: /webhook
  name: Webhook
- description: /ws
  name: 同步消息
- description: /message
  name: 消息
- description: /pay
  name: 支付
- description: /friend
  name: 朋友
- description: /user
  name: 用户
- description: /group
  name: 群管理
- description: /label
  name: 标签
- description: /applet
  name: 公众号/小程序
- description: /sns
  name: 朋友圈
- description: /finder
  name: 视频号
- description: /favor
  name: 收藏
- description: /qy
  name: 企业微信
- description: /equipment
  name: 设备
- description: /other
  name: 其他
- description: /monitor
  name: /monitor
- description: /rabbitmq
  name: /rabbitmq
- description: 文件上传、下载等操作
  name: 文件操作
- description: 视频上传、转发等操作
  name: 视频操作
- description: 图片上传、转发等操作
  name: 图片操作
- description: 消息转发相关操作
  name: 转发操作
x-lazy-loading-enabled: true
x-pagination-enabled: true
x-performance-optimized: true
